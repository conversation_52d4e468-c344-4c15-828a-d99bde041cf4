import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/generated/l10n.dart';

class PlaceVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final double? aspectRatio;
  final bool autoPlay;
  final bool showControls;
  final BoxFit? fit;

  const PlaceVideoPlayer({
    super.key,
    required this.videoUrl,
    this.aspectRatio,
    this.autoPlay = false,
    this.showControls = true,
    this.fit,
  });

  @override
  State<PlaceVideoPlayer> createState() => _PlaceVideoPlayerState();
}

class _PlaceVideoPlayerState extends State<PlaceVideoPlayer>
    with TickerProviderStateMixin {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isMuted = false;
  String? _errorMessage;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  @override
  void initState() {
    super.initState();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _initializeVideo();
    _startControlsTimer();
  }

  @override
  void dispose() {
    _controller?.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });

      final file = await DefaultCacheManager().getSingleFile(widget.videoUrl);
      _controller = VideoPlayerController.file(file);

      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isLoading = false;
          if (widget.autoPlay) {
            _controller!.play();
            _isPlaying = true;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _togglePlayPause() {
    if (_controller == null) return;

    setState(() {
      if (_isPlaying) {
        _controller!.pause();
        _isPlaying = false;
      } else {
        _controller!.play();
        _isPlaying = true;
      }
    });

    _showControlsTemporarily();
  }

  void _toggleMute() {
    if (_controller == null) return;

    setState(() {
      _isMuted = !_isMuted;
      _controller!.setVolume(_isMuted ? 0.0 : 1.0);
    });

    _showControlsTemporarily();
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    _controlsAnimationController.forward();
    _startControlsTimer();
  }

  void _startControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _onVideoTap() {
    if (widget.showControls) {
      _showControlsTemporarily();
    } else {
      _togglePlayPause();
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Container(
      width: double.infinity,
      height: 250,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Video player or loading/error states
            if (_isLoading)
              Container(
                color: Colors.grey[900],
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ),
              )
            else if (_hasError)
              Container(
                color: Colors.grey[900],
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.white,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        s.failedToLoadVideo,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_errorMessage != null)
                        Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _initializeVideo,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.accentColor,
                        ),
                        child: Text(s.retry),
                      ),
                    ],
                  ),
                ),
              )
            else if (_controller != null && _controller!.value.isInitialized)
              GestureDetector(
                onTap: _onVideoTap,
                child: widget.aspectRatio != null
                    ? AspectRatio(
                        aspectRatio: widget.aspectRatio!,
                        child: VideoPlayer(_controller!),
                      )
                    : SizedBox.expand(
                        child: FittedBox(
                          fit: widget.fit ?? BoxFit.contain,
                          child: SizedBox(
                            width: _controller!.value.size.width,
                            height: _controller!.value.size.height,
                            child: VideoPlayer(_controller!),
                          ),
                        ),
                      ),
              ),

            // Video controls overlay
          ],
        ),
      ),
    );
  }
}
