import 'package:flutter/material.dart';
import 'package:pod_player/pod_player.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/generated/l10n.dart';

class PlaceVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final double? aspectRatio;
  final bool autoPlay;
  final bool showControls;

  const PlaceVideoPlayer({
    super.key,
    required this.videoUrl,
    this.aspectRatio,
    this.autoPlay = false,
    this.showControls = true,
  });

  @override
  State<PlaceVideoPlayer> createState() => _PlaceVideoPlayerState();
}

class _PlaceVideoPlayerState extends State<PlaceVideoPlayer>
    with TickerProviderStateMixin {
  PodPlayerController? _podPlayerController;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isMuted = false;
  String? _errorMessage;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  @override
  void initState() {
    super.initState();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _initializeVideo();
    _startControlsTimer();
  }

  @override
  void dispose() {
    _betterPlayerController?.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });

      // Configure BetterPlayer for place details
      final betterPlayerConfiguration = BetterPlayerConfiguration(
        aspectRatio: widget.aspectRatio ?? 16 / 9,
        autoPlay: widget.autoPlay,
        looping: false,
        startAt: Duration.zero,
        fullScreenByDefault: false,
        allowedScreenSleep: true,
        fit: BoxFit.cover,
        placeholder: Container(
          color: Colors.grey[900],
          child: const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
            ),
          ),
        ),
        showPlaceholderUntilPlay: true,
        controlsConfiguration: BetterPlayerControlsConfiguration(
          enableProgressText: true,
          enableProgressBar: true,
          enablePlayPause: widget.showControls,
          enableMute: widget.showControls,
          enableFullscreen: widget.showControls,
          showControls: widget.showControls,
          controlsHideTime: const Duration(seconds: 3),
        ),
      );

      final betterPlayerDataSource = BetterPlayerDataSource(
        BetterPlayerDataSourceType.network,
        widget.videoUrl,
        cacheConfiguration: const BetterPlayerCacheConfiguration(
          useCache: true,
          preCacheSize: 5 * 1024 * 1024, // 5MB pre-cache
          maxCacheSize: 50 * 1024 * 1024, // 50MB max cache
          maxCacheFileSize: 25 * 1024 * 1024, // 25MB max file size
        ),
        headers: {
          'User-Agent': 'GatherPoint/1.0 (Flutter)',
          'Accept': 'video/mp4,video/*,*/*',
        },
      );

      _betterPlayerController = BetterPlayerController(
        betterPlayerConfiguration,
        betterPlayerDataSource: betterPlayerDataSource,
      );

      // Add event listener
      _betterPlayerController!.addEventsListener((event) {
        if (mounted) {
          switch (event.betterPlayerEventType) {
            case BetterPlayerEventType.initialized:
              setState(() {
                _isLoading = false;
                _hasError = false;
              });
              break;
            case BetterPlayerEventType.play:
              setState(() {
                _isPlaying = true;
              });
              break;
            case BetterPlayerEventType.pause:
              setState(() {
                _isPlaying = false;
              });
              break;
            case BetterPlayerEventType.exception:
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = 'Video codec not supported on this device';
              });
              break;
            default:
              break;
          }
        }
      });

    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _togglePlayPause() {
    if (_betterPlayerController == null) return;

    if (_isPlaying) {
      _betterPlayerController!.pause();
    } else {
      _betterPlayerController!.play();
    }

    _showControlsTemporarily();
  }

  void _toggleMute() {
    if (_betterPlayerController == null) return;

    setState(() {
      _isMuted = !_isMuted;
      _betterPlayerController!.setVolume(_isMuted ? 0.0 : 1.0);
    });

    _showControlsTemporarily();
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    _controlsAnimationController.forward();
    _startControlsTimer();
  }

  void _startControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _onVideoTap() {
    if (widget.showControls) {
      _showControlsTemporarily();
    } else {
      _togglePlayPause();
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Container(
      width: double.infinity,
      height: 250,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Video player or loading/error states
            if (_isLoading)
              Container(
                color: Colors.grey[900],
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ),
              )
            else if (_hasError)
              Container(
                color: Colors.grey[900],
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.white,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        s.failedToLoadVideo,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_errorMessage != null)
                        Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _initializeVideo,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.accentColor,
                        ),
                        child: Text(s.retry),
                      ),
                    ],
                  ),
                ),
              )
            else if (_betterPlayerController != null)
              GestureDetector(
                onTap: _onVideoTap,
                child: BetterPlayer(controller: _betterPlayerController!),
              ),

            // BetterPlayer handles its own controls when enabled
          ],
        ),
      ),
    );
  }
}
