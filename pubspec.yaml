name: gather_point
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+3

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  cached_network_image: ^3.3.1
  conditional_builder_null_safety: ^0.0.6
  dartz: ^0.10.1
  dio: ^5.4.0
  equatable: ^2.0.5
  flutter_bloc: ^8.1.4
  flutter_localizations:
    sdk: flutter
  flutter_native_splash: ^2.2.16
  flutter_svg: ^2.0.10+1
  get_it: ^7.6.4
  go_router: ^13.2.0
  google_fonts: ^6.2.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  oktoast: ^3.4.0
  geolocator: ^10.0.0
  image_picker: ^0.8.7+3
  file_picker: ^8.1.2
  path_provider: ^2.0.15
  meta: ^1.9.1
  pod_player: ^0.2.2  # Modern video player with better codec support
  # video_player: ^2.7.0  # For video playback - replaced with pod_player
  flutter_cache_manager: ^3.3.0
  flutter_map: ^6.0.0
  latlong2: ^0.9.0
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  dropdown_button2: ^2.3.9
  intl: ^0.20.2
  pretty_dio_logger: ^1.4.0
  logger: ^2.5.0
  # chucker_flutter: ^1.8.0
  connectivity_plus: ^6.1.2
  dots_indicator: ^3.0.0
  pinput: ^5.0.0
  persistent_bottom_nav_bar_v2: ^5.2.3
  carousel_slider: ^5.0.0
  audioplayers: ^6.1.0
  shared_preferences: ^2.0.0
  table_calendar: ^3.1.0
  reorderables: ^0.6.0
  firebase_core: ^3.0.0
  firebase_messaging: ^15.0.0
  flutter_local_notifications: ^18.0.0
  firebase_analytics: ^11.0.0
  timeago: ^3.6.1
  url_launcher: ^6.3.1
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1
  crypto: ^3.0.6
  syncfusion_flutter_calendar: ^29.2.10
  sms_autofill: ^2.4.1
  shimmer: ^3.0.0
  share_plus: ^11.0.0
  flutter_markdown: ^0.7.4+1
  webview_flutter: ^4.4.2
dev_dependencies:
  intl_utils: ^2.8.7
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/temp/
    - assets/gifs/
    - assets/sounds/
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # Custom fonts - Suisse International
  fonts:
    - family: SuisseIntl
      fonts:
        - asset: assets/fonts/suisseintl_regular.otf
          weight: 400
        - asset: assets/fonts/suisseintl_bold.ttf
          weight: 700
flutter_intl:
  enabled: true

flutter_assets:
  assets_path:
    - assets/images/
    - assets/icons/
    - assets/temp/
    - assets/gifs/
  output_path: lib/core/utils
  filename: app_assets.dart
  classname: AppAssets
  field_prefix:
  ignore_comments: true